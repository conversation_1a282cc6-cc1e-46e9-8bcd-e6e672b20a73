from selenium import webdriver  # # 驱动浏览器
from selenium.webdriver.common.by import By  # 选择器
from selenium.webdriver.support.wait import WebDriverWait  # 等待页面加载完毕，寻找某些元素
from selenium.webdriver.support import expected_conditions as EC  # 等待指定标签加载完毕
from selenium.common.exceptions import NoSuchElementException
from fake_useragent import UserAgent
import time

import config
from log_config import logger


class LoginNio:
    def __init__(self, url: str, element: str):
        if len(config.USER) == 0 or len(config.PASSWORD) == 0:
            logger.error(f"请先补全 {url} 页面的登录信息，至 {config} 文件!")
            exit()

        self.url = url
        self.element_id = element

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument(f"--user-agent={UserAgent().chrome}")  # 设置请求头的User-Agent
        # chrome_options.add_argument('--headless')  # 浏览器不提供可视化页面

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.implicitly_wait(5)
        self.wait = WebDriverWait(self.driver, 10)  # 设置等待时间为10秒
        self.__login()

    def __login(self):
        self.driver.get(self.url)
        try:
            time.sleep(3)
            self.driver.find_element(By.ID, self.element_id)
        except NoSuchElementException:
            self.wait.until(EC.presence_of_element_located((By.ID, "username")))
            user_elem = self.driver.find_element(By.ID, "username")
            user_elem.send_keys(config.USER)

            self.wait.until(EC.presence_of_element_located((By.ID, "pwd")))
            pwd_elem = self.driver.find_element(By.ID, "pwd")
            pwd_elem.send_keys(config.PASSWORD)

            self.wait.until(EC.presence_of_element_located((By.ID, "loginBtn")))
            login_btn = self.driver.find_element(By.ID, "loginBtn")
            # 检查用户和密码字段是否完整
            if login_btn.is_enabled():
                login_btn.click()
            else:
                logger.error("Please specify the USER and PASSWORD in the config.py file!")
                exit()

            # 检查用户和密码是否正确
            try:
                self.driver.get(self.url)
                time.sleep(2)
                self.driver.find_element(By.ID, self.element_id)
            except NoSuchElementException:
                logger.error("Please check if the USER and PASSWORD in config.py are correct!")
                exit()
        else:
            logger.info("Already logged in!")

    def refresh(self):
        self.__login()

    def get_cookie(self):
        cookie_dict = {}
        dict_list = self.driver.get_cookies()
        for i in dict_list:
            name = i.get("name")
            value = i.get("value")
            if name and value:
                cookie_dict[name] = value

        self.driver.quit()
        return cookie_dict
