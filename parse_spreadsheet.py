import os
import datetime
import openpyxl
from openpyxl.styles import Pattern<PERSON>ill
from openpyxl.styles.colors import Color
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter

from log_config import logger


class Spreadsheet:
    def __init__(self, path: str):
        if not os.path.isfile(path):
            wb = openpyxl.Workbook()
            wb.save(path)
            wb.close()

        self.path = path
        self.wb = openpyxl.load_workbook(self.path)
        self.sheet = None

    def get_header_list(self, header_row_num=1, default_header="ECU") -> list[str]:
        header_list = []
        header_row = self.sheet[header_row_num]
        for i in header_row:
            header_name = i.value
            if header_name is None:
                header_name = default_header
            header_list.append(header_name)
        return header_list

    def close_file(self):
        self.wb.save(self.path)
        self.wb.close()

    def remove_other_sheets(self):
        sheet_names = self.wb.sheetnames
        for sheet_name in sheet_names[1:]:
            self.wb.remove(self.wb[sheet_name])

        self.close_file()


def fill_color(rgb: str, sheet):
    color_rgb = Color(rgb=rgb)
    pattern_fill = PatternFill(start_color=color_rgb, end_color=color_rgb, fill_type="solid")
    for cell in sheet[sheet.max_row]:
        cell.fill = pattern_fill


class VehicleSpreadsheet(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)
        self.need_clean_overview_sheet = True

    def get_vins(self) -> list[str]:
        self.sheet = self.wb.worksheets[0]
        vin_set = set()

        index = 0
        header_list = self.get_header_list(1)
        vin_index = None
        for header_name in header_list:
            if "vin" in header_name.lower():
                vin_index = index
            index = index + 1

        if vin_index is None:
            logger.error(f"Failed to get vin index in {self.path}")
            return []

        for row in list(self.sheet.rows)[1:]:
            value = row[vin_index].value
            if value and value.isalnum() and len(value) == 17:
                vin_set.add(value)
            else:
                logger.warning(f"Invalid vin value: {value}")
        return list(vin_set)

    def get_vin_stage(self) -> list[tuple]:
        self.sheet = self.wb.worksheets[0]
        vin_stage_list = []

        index = 0
        header_list = self.get_header_list(1)
        vin_index = None
        stage_index = None
        for header_name in header_list:
            if "vin" in header_name.lower():
                vin_index = index
            elif "生产阶段" in header_name:
                stage_index = index
            index = index + 1

        if vin_index is None:
            logger.error(f"Failed to get vin index in {self.path}")
            return []

        for row in list(self.sheet.rows)[1:]:
            vin_value = row[vin_index].value
            stage_value = row[stage_index].value
            if vin_value and vin_value.isalnum() and len(vin_value) == 17:
                vin_stage_list.append((vin_value, stage_value))
        return vin_stage_list

    def save_data(self, vin, vid, ecu_info_list):
        # 总览表 sheet
        if "总览表" not in self.wb.sheetnames:
            overview_sheet = self.wb.create_sheet("总览表")
            self.need_clean_overview_sheet = False
        else:
            overview_sheet = self.wb["总览表"]

        if self.need_clean_overview_sheet:
            overview_sheet.delete_rows(1, overview_sheet.max_row)
            self.need_clean_overview_sheet = False

        header = ["VIN", "ECU", "软硬件零件号采样时间", "是否兼容", "不兼容情况说明", "当前硬件零件号", "目标版本所支持的硬件零件号"]
        overview_sheet.append(header)
        fill_color("C0C0C0", overview_sheet)

        for i in range(len(header)):
            overview_sheet.column_dimensions[get_column_letter(i + 1)].width = 20
        overview_sheet.column_dimensions[get_column_letter(2)].width = 10
        overview_sheet.column_dimensions[get_column_letter(5)].width = 40
        overview_sheet.column_dimensions[get_column_letter(7)].width = 60

        # 获取不到 ECU 软硬件零件号的车辆
        if len(ecu_info_list) == 1:
            overview_sheet.append([ecu_info_list[0]])
            overview_sheet.append([])
            self.close_file()
            return

        # 每个车辆软硬件兼容情况的详细表
        if vin not in self.wb.sheetnames:
            detail_sheet = self.wb.create_sheet(vin)
        else:
            detail_sheet = self.wb[vin]
            detail_sheet.delete_rows(1, detail_sheet.max_row)

        detail_sheet.append(["VIN", vin])
        detail_sheet.append(["VID", vid])
        detail_sheet.append([])
        header = ["ECU", "软硬件零件号采样时间", "当前软件零件号", "目标软件零件号", "当前硬件零件号", "目标软件版本所支持的硬件零件号",
                  "是否兼容", "不兼容情况说明", "软件是否需要升级", "软件零件号"]
        detail_sheet.append(header)
        fill_color("C0C0C0", detail_sheet)

        # 列宽
        for i in range(len(header)):
            detail_sheet.column_dimensions[get_column_letter(i + 1)].width = 20

        # 按照 ECU 名称排序
        ecu_info_list.sort(key=lambda a_list: a_list[0])
        for i in ecu_info_list:
            detail_sheet.append(i)
            match i[6]:
                case "软硬件都不兼容":
                    overview_sheet.append([vin, i[0], i[1], i[6], i[7], i[4], i[5]])
                    fill_color("FFC0CB", detail_sheet)
                    fill_color("FFC0CB", overview_sheet)
                case "仅软件兼容":
                    overview_sheet.append([vin, i[0], i[1], i[6], i[7], i[4], i[5]])
                    fill_color("FFA07A", detail_sheet)
                    fill_color("FFA07A", overview_sheet)
                case "不确定":
                    overview_sheet.append([vin, i[0], i[1], i[6], i[7]])
                    fill_color("FFA07A", detail_sheet)
                    fill_color("FFA07A", overview_sheet)
                case "跳过" | "仅硬件兼容":
                    fill_color("FFFFE0", detail_sheet)
                case "软硬件都兼容":
                    match i[8]:
                        case "需要":
                            fill_color("ADD8E6", detail_sheet)
                        case "高于目标版本":
                            fill_color("7FFFD4", detail_sheet)
                        case "不需要":
                            font = Font(strike=True)
                            for cell in detail_sheet[detail_sheet.max_row]:
                                cell.font = font

        overview_sheet.append([f"详细情况，请查看 {vin} sheet"])
        overview_sheet.append([])
        self.close_file()

    def save_ecu_version(self, vin, vid, sample_time_str, ecu_info_dict):
        first_save = False
        if vin not in self.wb.sheetnames:
            first_save = True
            detail_sheet = self.wb.create_sheet(vin)
        else:
            detail_sheet = self.wb[vin]

        if first_save:
            detail_sheet.column_dimensions[get_column_letter(1)].width = 16
            detail_sheet.column_dimensions[get_column_letter(2)].width = 16
            detail_sheet.append(["VIN", vin])
            detail_sheet.append(["VID", vid])
            detail_sheet.append(["软硬件零件号采样时间"])
            detail_sheet.append([])

            ecu_list = list(ecu_info_dict.keys())
            ecu_list.sort()
            for i in ecu_list:
                detail_sheet.append([i, "软件零件号"])
                detail_sheet.append(["", "硬件零件号"])

        next_column_num = detail_sheet.max_column + 1
        detail_sheet.cell(row=3, column=next_column_num, value=f"{sample_time_str}")
        detail_sheet.column_dimensions[get_column_letter(next_column_num)].width = 20
        for row in detail_sheet.iter_rows(min_row=5, max_row=detail_sheet.max_row):
            row_num = row[0].row
            if row_num % 2 == 0:
                continue
            # 在每一行后面增加新的列
            ecu_name = row[0].value
            last_software_pn = detail_sheet.cell(row=row_num, column=next_column_num - 1).value
            last_hardware_pn = detail_sheet.cell(row=row_num + 1, column=next_column_num - 1).value
            if last_software_pn is None:
                last_software_pn = ""
            if last_hardware_pn is None:
                last_hardware_pn = ""

            if ecu_name not in ecu_info_dict.keys():
                current_software_pn = "此 ECU 已隐藏"
                current_hardware_pn = "此 ECU 已隐藏"
            else:
                current_software_pn = ecu_info_dict.get(ecu_name)[0]
                current_hardware_pn = ecu_info_dict.get(ecu_name)[1]

            detail_sheet.cell(row=row_num, column=next_column_num, value=current_software_pn)
            detail_sheet.cell(row=row_num + 1, column=next_column_num, value=current_hardware_pn)

            # 为变化的单元格填充浅黄色
            color_rgb = Color(rgb="FFFFCC")
            pattern_fill = PatternFill(start_color=color_rgb, end_color=color_rgb, fill_type="solid")
            if next_column_num > 3 and current_software_pn != last_software_pn:
                detail_sheet.cell(row=row_num, column=next_column_num - 1).fill = pattern_fill
                detail_sheet.cell(row=row_num, column=next_column_num).fill = pattern_fill
            if next_column_num > 3 and current_hardware_pn != last_hardware_pn:
                detail_sheet.cell(row=row_num + 1, column=next_column_num - 1).fill = pattern_fill
                detail_sheet.cell(row=row_num + 1, column=next_column_num).fill = pattern_fill

            self.close_file()


class Compatibility(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)

    def get_118_110_index(self) -> tuple:
        index = 0
        header_list = self.get_header_list(2)
        software_pn = 0
        hardware_pn = 0
        for header_name in header_list:
            if "Software PN" in header_name or "F1 18" in header_name:
                software_pn = index
            if "Hardware PN" in header_name or "F1 10" in header_name:
                hardware_pn = index
            index = index + 1

            if software_pn > 0 and hardware_pn > 0:
                return software_pn, hardware_pn

        if software_pn == 0 or hardware_pn == 0:
            logger.error("Failed to get Software PN or Hardware PN index in sheet")
            return None, None

    def parse_data(self) -> dict:
        ecu_dict = {}
        """
        ecu_dict = {
            "ecu_1":
                [
                    ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
                    ("software_pn_2", ["hardware_pn_2", "hardware_pn_2"])
                ],
            "ecu_2":
                [
                    ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
                ]
        }
        """
        self.sheet = self.wb.active
        if self.sheet.max_row < 3:
            logger.error(f"{self.path} row count < 3")
            return ecu_dict

        software_pn_index, hardware_pn_index = self.get_118_110_index()
        if software_pn_index is None or hardware_pn_index is None:
            return ecu_dict

        pre_ecu_name = ""
        is_calib = False
        for row in list(self.sheet.rows)[2:]:
            ecu_name = row[0].value
            software_pn = row[software_pn_index].value
            hardware_pn = row[hardware_pn_index].value

            sw_part_name = row[software_pn_index - 1].value
            if sw_part_name:
                sw_part_name_lower = str(sw_part_name).lower()
                if "calib" in sw_part_name_lower:
                    is_calib = True
                    continue
                else:
                    is_calib = False

            if ecu_name and software_pn:
                # ECU 和 software_pn 都不为空时，此时是一个新的 ECU (但 ECU 名字可能相同，比如 NOMI 等)
                pre_ecu_name = ecu_name
                if ecu_name not in ecu_dict.keys():
                    if hardware_pn is None:
                        ecu_dict[ecu_name] = [(software_pn, [])]
                    else:
                        ecu_dict[ecu_name] = [(software_pn, [hardware_pn])]
                else:
                    # 相同的 ECU，比如 NOMI
                    if hardware_pn is None:
                        ecu_dict[ecu_name].append((software_pn, []))
                    else:
                        ecu_dict[ecu_name].append((software_pn, [hardware_pn]))

            elif ecu_name is None and software_pn is None:
                # ECU 和 software_pn 都为空时，此时的 hardware_pn 还是之前 ECU 的
                if is_calib:
                    # 还是在标定的行里
                    continue
                else:
                    ecu_dict[pre_ecu_name][-1][-1].append(hardware_pn)

        need_to_add_key = []
        need_to_delete_key = []
        for key in ecu_dict.keys():
            key_list = []
            if "/" in key:
                key_list = key.split("/")
                need_to_delete_key.append(key)
            elif "\\" in key:
                key_list = key.split("\\")
                need_to_delete_key.append(key)
            for i in key_list:
                need_to_add_key.append((i, key))
        for t in need_to_add_key:
            new_key = t[0]
            delete_key = t[-1]
            if new_key in ecu_dict.keys():
                ecu_dict[new_key] += ecu_dict[delete_key]
            else:
                ecu_dict[new_key] = ecu_dict[delete_key]
        for delete_key in need_to_delete_key:
            del ecu_dict[delete_key]

        return ecu_dict


class DID(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)

    def get_vehicle_model(self) -> str:
        self.sheet = self.wb['车辆及 BD2 账户信息']
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]
            value = row[1]
            if key == "型号":
                return value

    def get_ecu_version(self) -> dict:
        sample_time = ""
        self.sheet = self.wb['车辆及 BD2 账户信息']
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]
            value = row[1]
            if key == "报告生成时间":
                sample_time = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M').timestamp()
                break

        ecu_version_map = {}
        self.sheet = self.wb['标识信息']
        for row in self.sheet.iter_rows(min_row=2, values_only=True):
            ecu = row[0].split()[0]
            key = row[2]
            value = row[3]
            if key and value and ecu:
                if ecu not in ecu_version_map.keys():
                    ecu_version_map[ecu] = {"ecu": ecu}

                if key == "F110":
                    ecu_version_map[ecu]["hardware_pn"] = value
                elif key == "F118":
                    ecu_version_map[ecu]["software_pn"] = value

        ecu_did = {"sample_time": sample_time, "ecu_version_map": ecu_version_map}

        return ecu_did


class ReplaceHardware(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)
        self.recorded_vehicle_model = set()
        self.header = []

    def add_header(self, vehicle_model: str, ecu_names: list):
        self.recorded_vehicle_model.add(vehicle_model)

        if "车辆改制明细" not in self.wb.sheetnames:
            detail_sheet = self.wb.create_sheet("车辆改制明细")
        else:
            detail_sheet = self.wb["车辆改制明细"]

        detail_sheet.append([])
        detail_sheet.append(["ECU 列，数字 1 表示硬件不兼容；数字 2 表示硬件零件号为空，数字 3 表示硬件零件号为乱码"])
        self.header = \
            ["车型", "序号", "生产阶段", "改制批次", "VIN", "改制地点", "是否改制完成", "改制总金额", "仅ADAS对手件改制金额"] \
            + ecu_names + ["采样时间"]
        detail_sheet.append(self.header)
        detail_sheet.append(["车辆报废前改制需求"])
        detail_sheet.append(["车辆报废后改制需求"])
        logger.info(f"Add {vehicle_model} header.")
        self.close_file()

    def save_row(self, model: str, vin: str, stage: str, sample_time: str, replaced_hardware: list):
        detail_sheet = self.wb["车辆改制明细"]
        one_row = [""] * len(self.header)
        one_row[0] = model
        one_row[2] = stage
        one_row[4] = vin
        if sample_time:
            one_row[-1] = sample_time
            for hardware, reason in replaced_hardware:
                if hardware in self.header:
                    index = self.header.index(hardware)
                    one_row[index] = reason
            detail_sheet.append(one_row)
        else:
            one_row[9] = replaced_hardware[0]
            detail_sheet.append(one_row)
            fill_color("FFA07A", detail_sheet)

        self.close_file()


class Bom(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)

    def get_hw_pns(self) -> set[str]:
        # 获取 sheet 名为 "EBOM" 的 sheet
        self.sheet = self.wb["EBOM"]

        hw_pn_index = 2
        hw_pn_set = set()
        for row in list(self.sheet.rows)[1:]:
            pn = row[hw_pn_index].value
            revison = row[hw_pn_index+1].value
            if pn:
                hw_pn_set.add(pn + " " + revison)
            else:
                logger.warning(f"Invalid hardware PN value: {value}")
        return hw_pn_set
