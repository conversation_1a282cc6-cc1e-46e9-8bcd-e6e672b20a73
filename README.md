# 使用说明

## 参数说明
```
usage: main.py [-h] [-v VIN [VIN ...]] [-p PATH] [-r REPLACE] [-e] [-d]

options:
  -h, --help            show this help message and exit
  -v VIN [VIN ...], --vin VIN [VIN ...]
                        车辆 VIN
  -p PATH, --path PATH  XXX.xlsx 文件路径，存储需要查询车辆 VIN
  -r REPLACE, --replace REPLACE
                        XXX 车辆需更换硬件.xlsx 文件路径
  -e, --ecu             只保存车辆 ECU 软硬件零件号
  -d, --did             使用标识文件，若不指定则从 TVAS 上读取
```

## 使用示例

### 查看指定车辆的硬件和软件是否与目标整车软件包兼容

1. 从凤凰平台(NT2)或SAM平台(NT3)下载目标整车软件包的兼容性 XXX.xlsx 文件, 至compatibility_sheet 文件夹下。  
也可从FOTA平台下载整车软件包的兼容性文件, [蔚来品牌](https://fota-web-stg.nioint.com/v2/#/version/software)
[乐道品牌](https://fota-web-onvo-stg.nioint.com/v2/#/version/software)，在此处下载的文件需要在Windows系统下打开，并另存覆盖原文件一次，不然会出现格式错误，无法解析的问题。

    > 若下载的文件名中不包含车型名称，则需要手动修改文件名。
例如，下载的 .xlsx 文件名为 "V0069900 CE_Package_Compatibility_1.0.xlsx"，则需要将文件名修改为 "DOM_V0069900 CE_Package_Compatibility_1.0.xlsx"，其中 DOM 为车型名称。车型大小写无所谓，程序中会统一成小写进行比较。

2. 指定车辆，又两种途径：
    - 命令行参数：`-v VIN1 VIN2 VIN3...`
    - 包含指定车辆 VIN 的 .xlsx 文件：`-p XXX.xlsx`, 车辆 vin 列需设置标题行为 "VIN"或"vin"。

3. 运行程序，查看是否兼容。
    ````
    # 查看 VIN1 车辆是否兼容，结果保存在新建的 .xlsx 文件中。
    python main.py -v VIN1

    # 例如：
    python main.py -v HJNAABGF2RB542058
    #  HJNAABGF2RB542058_软件刷写硬件兼容情况_20241010_143654.xlsx 为程序输出结果
    ````
    ````
    python main.py -p XXX.xlsx # 查看 XXX.xlsx 文件中所有车辆是否兼容，XXX.xlsx 文件会新增 sheet 以保存结果。
    ````
