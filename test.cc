#include <iostream>

using namespace std;

class Solution 
{
public:
    int minNumberOfFrogs(string croakOfFrogs) 
    {
        int count[5] = {0};
        int current = 0, max_frogs = 0;
        char map[256] = {};
        map['c'] = 0; map['r'] = 1; map['o'] = 2; map['a'] = 3; map['k'] = 4;

        for(auto ch : croakOfFrogs)
        {
            int idx = map[ch];
            if(idx == 0)
            {
                count[idx]++;
                current++; // 新青蛙开始
            }
            else if(idx == 4)
            {
                if(count[idx - 1] <= 0) return -1; // 青蛙匹配顺序不对
                count[idx - 1]--;
                current--; // 青蛙结束
            }
            else
            {
                if(count[idx - 1] <= 0) return -1;
                count[idx - 1]--;
                count[idx]++;
            }
            max_frogs = max(max_frogs, current);
        }
        return current == 0 ? max_frogs : -1;
    }
};

int main() {
    // char map[256] = {};
    // map['c'] = 0; map['r'] = 1; map['o'] = 2; map['a'] = 3; map['k'] = 4;
    
    // std::cout << "map['c'] = " << (int)map['c'] << std::endl; // 输出 0
    // std::cout << "map['r'] = " << (int)map['r'] << std::endl; // 输出 1
    // std::cout << "map[99] = " << (int)map[99] << std::endl;   // 输出 0 ('c'的ASCII是99)
    // return 0;
}