#!/usr/bin/python3

import os
import datetime
import argparse

import login_nio
import get_ecu_version
import parse_spreadsheet
from log_config import logger

vehicle_model_dict = {
    "Aries": "ES8",
    "Force": "ET7",
    "Gemini": "ES7",
    "Libra": "EC6",
    "Lyra": "EC7",
    "Orion": "ET5T",
    "Pegasus": "ET5",
    "Sirius": "ES6",
}


# 10 或 13位 时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


# 从标识报告中获取 ECU 的版本
def get_ecu_version_by_did(vin: str) -> tuple:
    path = ""
    did_sheet_dir = os.path.join(dir_path, "did_sheet")
    for file in os.listdir(did_sheet_dir):
        if vin in file:
            path = os.path.join(did_sheet_dir, file)
            break
    if not path:
        logger.warning(f"No did sheet for {vin}")
        return None, None

    did = parse_spreadsheet.DID(path)
    return did.get_vehicle_model(), did.get_ecu_version()


# 从TVAS上获取 ECU 的版本
def get_ecu_version_by_tvas(vin: str):
    res = get_ecu_version_request.query_by_vin(vin)
    if res is None:
        return None, None

    return res.get("vehicle_model"), get_ecu_version_request.query_ecu_version(vin)


# 从 ONVO FOTA WEB上获取 ECU 的版本
def get_ecu_version_by_onvo_fota_web(vin: str):
    url = "https://fota-web-onvo-stg.nioint.com/v2/#/fota/vehicles"
    element_id = "app"
    login = login_nio.LoginNio(url, element_id)
    cookie_dict = login.get_cookie()
    cookie_str = "; ".join([str(x) + "=" + str(y) for x, y in cookie_dict.items()])
    get_ecu_version_by_fota = get_ecu_version.GetEcuVersionByFota(cookie_str)

    vehicle_info_dict = get_ecu_version_by_fota.query_vehicle_info(vin)
    if vehicle_info_dict and vehicle_info_dict.get("vid"):
        return get_ecu_version_by_fota.query_ecu_version_by_fota(vehicle_info_dict.get("vid"))

    return None


# FOTA WEB上获取 ECU 的版本
def get_ecu_version_by_fota_web(vin: str):
    cookie = "FOTA-LGUSER=falcon.fan; lang=zh_CN; tenant=nio_ota; page-gateway-secure-sid-cn-prod=s%3AhPm_-cqugxefEQb0Vqn79uH4YOfO3N9m.C6ZHCArvBz9sxYehVckuWjpzAJQ4NUGBLDK%2BhsXGANY; page-gateway-secure-sid-plm=s_hPm_-cqugxefEQb0Vqn79uH4YOfO3N9m.0ba647080aef073f6cc587a155c92e5a3a730094383541812c32be86c5c600d6; proxyserver_prod=s%3AuaFcGPpcc3jgc1NzAxfJigKbi8YCe7jP.twuzuxV%2BEh9nCidY1xCLqus7LZnnJz28y2g5qX59Tzc; NIO-FOTA=MTcyNDk4MzI2NnxEdi1CQkFFQ180SUFBUkFCRUFBQV9nRmtfNElBQndaemRISnBibWNNQkFBQ2JIUUdjM1J5YVc1bkRBVUFBMU5UVHdaemRISnBibWNNQkFBQ2RHc0djM1J5YVc1bkREMEFPekl1TUVNelJrazNORFpTVURSR1ZsY3lURUpJUjFwVFNsRklOakkyV2tvMVZrWldSVUZCVVZJMU5UVTJVRmxUTnpWQlVrUkJUVUV0TFMwdEJuTjBjbWx1Wnd3RUFBSmpiQVp6ZEhKcGJtY01RZ0JBTkRFMFptTmxNMlUzWWpjM01HUTVPRFZrTWpOa05EVXhZbVF3WkRobFlqYzNZV1E0WW1Vd1lqZ3pOR0k0WldZeVl6WXpOR1JrWWpnelpXWXpaVE01TndaemRISnBibWNNQkFBQ2FXUUdjM1J5YVc1bkRDWUFKR00zWXpabE16RTRMVGczTmpBdE9EbGpaUzFoTXpBeExXSTFOV0ZtTWpRNE1tVTNZd1p6ZEhKcGJtY01CQUFDYVhBR2MzUnlhVzVuREE0QURERXdMakV4TVM0MExqRXlNd1p6ZEhKcGJtY01CQUFDYzNRRmFXNTBOalFFQmdEOHphSlB4QVp6ZEhKcGJtY01CQUFDZEdVR2MzUnlhVzVuREFrQUIyNXBiMTl2ZEdFPXwkarJh_RlYKstY6nawkHeFjgb7OR_7Qa-xqPA6hOLXYg==; fota-clientid=47e1095d001de095b51788d3b8e87c01; page-gateway-sid-cn-prod=s%3AGPK3KpjX2tCeVjR1MFMasWUEE7wXOUhM.G8xaJfK6SNmyYJyabLgoKRlIvY1BPBfXwqXzIDbmyz4; page-gateway-sid-plm=s_GPK3KpjX2tCeVjR1MFMasWUEE7wXOUhM.1bcc5a25f2ba48d9b2609c9a6cb828291948bd8d413c17d7c2a5f32036e6cb3e; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22falcon.fan%22%2C%22first_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThhY2YyYzA2MTdlYWEtMDRkOWVmMDBlOWJjNmY4LTI2MDMxZTUxLTE0NzQ1NjAtMThhY2YyYzA2MTgyOWViIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiZmFsY29uLmZhbiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22falcon.fan%22%7D%2C%22%24device_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%7D"
    get_ecu_version_by_fota = get_ecu_version.GetEcuVersionByFota(cookie, "https://fota-web-stg.nioint.com/")

    vehicle_info_dict = get_ecu_version_by_fota.query_vehicle_info(vin)
    if vehicle_info_dict and vehicle_info_dict.get("vid"):
        return get_ecu_version_by_fota.query_ecu_version_by_fota(vehicle_info_dict.get("vid"))

    return None


def get_hardware_compatibility_dict(vehicle_model: str):
    path = ""
    vehicle_name = vehicle_model_dict.get(vehicle_model)
    logger.debug(f"{vehicle_model}: {vehicle_name}")
    compatibility_sheet_dir = os.path.join(dir_path, "compatibility_sheet")
    for file in os.listdir(compatibility_sheet_dir):
        if vehicle_model.lower() in file.lower() or (vehicle_name and vehicle_name.lower() in file.lower()):
            path = os.path.join(compatibility_sheet_dir, file)
            break

    if not path:
        logger.error(f"请下载 {vehicle_model} 车型对应的软硬件兼容表到 {compatibility_sheet_dir}")
        return None
    elif not os.path.isfile(path):
        logger.error(f"Can't get hardware compatibility sheet with {path}")
        return None

    logger.info(f"Parse {path}")
    hardware_compatibility_sheet = parse_spreadsheet.Compatibility(path)
    compatibility_dict = hardware_compatibility_sheet.parse_data()

    return compatibility_dict


def is_messy_code(pn: str):
    if len(pn) != 0:
        pn_list = pn.strip().split()
        pn_length = len(pn_list)
        if pn_length != 2:
            logger.warning(f"PN length is {pn_length} != 2, is messy code!")
            return True
        for x in pn_list:
            if not x.isalnum():
                return True
    return False


def is_valid_pn(pn: str):
    if len(pn) == 0:
        return False
    if is_messy_code(pn):
        return False
    return True


def check_hardware_compatibility_for_one_vehicle(vin: str, spreadsheet):
    vehicle_model = None
    ecu_version_and_sample_time = None
    if args.did:
        vehicle_model, ecu_version_and_sample_time = get_ecu_version_by_did(vin)

    # 若本地没有标识文件，尝试从 TVAS 上抓取
    if ecu_version_and_sample_time is None:
        vehicle_model, ecu_version_and_sample_time = get_ecu_version_by_tvas(vin)
        if vehicle_model == "Leo":
            ecu_version_and_sample_time = get_ecu_version_by_fota_web(vin)
            if ecu_version_and_sample_time is None:
                spreadsheet.save_data(vin, None, [f"从 TVAS 和 FOTA 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                return
        else:
            if ecu_version_and_sample_time is None:
                if vehicle_model == "DOM":
                    ecu_version_and_sample_time = get_ecu_version_by_onvo_fota_web(vin)
                    if ecu_version_and_sample_time is None:
                        spreadsheet.save_data(vin, None, [f"从 TVAS 和 FOTA 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                        return
                else:
                    spreadsheet.save_data(vin, None, [f"从 TVAS 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                    return

    vid = ecu_version_and_sample_time.get("vehicle_id", "")
    sample_time = ecu_version_and_sample_time.get("sample_time")  # s
    sample_time_str = get_date_from_timestamp(sample_time)
    ecu_version_dict = ecu_version_and_sample_time.get("ecu_version_map", {})

    only_get_ecu_version = args.ecu
    hardware_compatibility_dict = {}
    if only_get_ecu_version:
        logger.info(f"Only save {vin} ECU software_pn and hardware_pn")
    else:
        hardware_compatibility_dict = get_hardware_compatibility_dict(vehicle_model)
        if hardware_compatibility_dict is None:
            spreadsheet.save_data(vin, vid, [f"没有解析到 {vehicle_model} 车型对应的软硬件兼容表"])
            return

    ecu_info_list = []
    ecu_info_dict = {}
    for key, value in ecu_version_dict.items():
        ecu_info = None
        ecu_name = value.get("ecu")
        if ecu_name not in hardware_compatibility_dict.keys():
            continue

        current_software_pn = value.get("software_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        current_hardware_pn = value.get("hardware_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())

        if is_messy_code(current_software_pn):
            current_software_pn = "乱码"
        if is_messy_code(current_hardware_pn):
            current_hardware_pn = "乱码"

        if only_get_ecu_version:
            ecu_info_dict[ecu_name] = (current_software_pn, current_hardware_pn)
            continue

        """
        软硬件的兼容情况各有三种：
        硬件：兼容（H√），不兼容（H×），无法获取零件号（H-）
        硬件：兼容（S√），不兼容（S×），无法获取零件号（S-）
        两两组合共有9种情况，根据实际需求进行合并：
        1. H-S√ H-S× H-S-  不确定，无法获取硬件零件号 橙色

        2. H√S√ 软硬件都兼容 蓝色或白色
        3. H√S× H√S- 仅硬件兼容，FOTA 时可临时修改其软件零件号 黄色

        4. H×S√ 仅软件兼容，软件零件号匹配，但不在所支持的硬件零件号中 橙色
        5. H×S× H×S- 软硬件都不兼容 红色
        """
        if not is_valid_pn(current_hardware_pn):
            # 1. H-S√ H-S× H-S-  不确定，无法获取硬件零件号
            logger.warning(
                f"{vin} {ecu_name} software_pn: {current_software_pn}, hardware_pn: {current_hardware_pn}, skip")
            ecu_info = [ecu_name, sample_time_str, current_software_pn, "", current_hardware_pn, "",
                        "不确定", "无法获取硬件零件号，或硬件零件号乱码"]
            ecu_info_list.append(ecu_info)
            continue

        if ecu_name in hardware_compatibility_dict.keys():
            software_pn_and_hardware_pn_list = hardware_compatibility_dict[ecu_name]
            software_matched = False
            hardware_matched = False
            hardware_pn_set = set()

            for software_pn, hardware_pn_list in software_pn_and_hardware_pn_list:
                target_software_num = software_pn.split()[0]
                target_software_version = software_pn.split()[-1]
                hardware_pn_set.update(set(hardware_pn_list))

                if current_hardware_pn in hardware_pn_list or len(hardware_pn_list) == 0:
                    hardware_matched = True
                    ecu_info = [ecu_name, sample_time_str, current_software_pn, software_pn,
                                current_hardware_pn, ",".join(hardware_pn_list)]

                    if is_valid_pn(current_software_pn) and current_software_pn.split()[0] == target_software_num:
                        #  2. H√S√ 软硬件都兼容
                        ecu_info.extend(["软硬件都兼容", ""])

                        current_software_version = current_software_pn.split()[-1]
                        if current_software_version == target_software_version:
                            logger.debug(f"{vin} {ecu_name}: {software_pn} = {target_software_version}")
                            ecu_info.extend(["不需要", f"{current_software_version} = {target_software_version}"])
                        elif current_software_version != "ZZ" and current_software_version > target_software_version:
                            logger.debug(f"{vin} {ecu_name}: {software_pn} > {target_software_version}")
                            ecu_info.extend(["高于目标版本", f"{current_software_version} > {target_software_version}"])
                        else:
                            logger.debug(f"{vin} {ecu_name}: {software_pn} -> {target_software_version}")
                            ecu_info.extend(["需要", f"{current_software_version} -> {target_software_version}"])
                        break
                    else:
                        # 3. H√S× H√S- 仅硬件兼容（FOTA 时可临时修改其软件零件号）
                        ecu_info.extend(["仅硬件兼容", "FOTA 时可临时修改其软件零件号"])
                        logger.debug(
                            f"{vin} {ecu_name} software num {target_software_num} is not matched,"
                            f" but current hardware pn: {current_hardware_pn} in CompatibleHW list: {hardware_pn_list}")

            if not hardware_matched and is_valid_pn(current_software_pn):
                for software_pn, hardware_pn_list in software_pn_and_hardware_pn_list:
                    target_software_num = software_pn.split()[0]
                    if current_software_pn.split()[0] == target_software_num:
                        #  4. H×S√ 仅软件兼容，软件零件号匹配，但不在所支持的硬件零件号中
                        software_matched = True
                        ecu_info = [ecu_name, sample_time_str, current_software_pn, software_pn,
                                    current_hardware_pn, ",".join(hardware_pn_list),
                                    "仅软件兼容", "硬件不在所支持的零件号中，酌情更换硬件"]
                        logger.error(
                            f"{vin} {ecu_name} software num {target_software_num} is matched, but current hardware pn: "
                            f"{current_hardware_pn} not in CompatibleHW list: {hardware_pn_list}")
                        break

            if not hardware_matched and not software_matched:
                # 5.H×S√ 软硬件都不兼容
                ecu_info = [ecu_name, sample_time_str, current_software_pn, " ",
                            current_hardware_pn, ",".join(hardware_pn_set),
                            "软硬件都不兼容", "需要更换硬件"]
                logger.error(f"{vin} {ecu_name} hardware is not matched")

        ecu_info_list.append(ecu_info)

    if only_get_ecu_version:
        spreadsheet.save_ecu_version(vin, vid, sample_time_str, ecu_info_dict)
    else:
        spreadsheet.save_data(vin, vid, ecu_info_list)


def get_replaced_hardware_for_one_vehicle(vin: str, stage: str, spreadsheet):
    vehicle_model = None
    ecu_version_and_sample_time = None
    if args.did:
        vehicle_model, ecu_version_and_sample_time = get_ecu_version_by_did(vin)

    # 若本地没有标识文件，尝试从 TVAS 上抓取
    if ecu_version_and_sample_time is None:
        vehicle_model, ecu_version_and_sample_time = get_ecu_version_by_tvas(vin)
        if vehicle_model == "Leo":
            ecu_version_and_sample_time = get_ecu_version_by_fota_web(vin)
            if ecu_version_and_sample_time is None:
                spreadsheet.save_row(vehicle_model, vin, stage, "", [f"从 TVAS 和 FOTA 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                return
        else:
            if ecu_version_and_sample_time is None:
                if vehicle_model == "DOM":
                    ecu_version_and_sample_time = get_ecu_version_by_onvo_fota_web(vin)
                    if ecu_version_and_sample_time is None:
                        spreadsheet.save_data(vehicle_model, vin, stage, "", [f"从 TVAS 和 FOTA 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                        return
                else:
                    spreadsheet.save_row(vin, None, [f"从 TVAS 上获取不到 {vin} 车辆 ECU 的软硬件零件号"])
                    return

    hardware_compatibility_dict = get_hardware_compatibility_dict(vehicle_model)
    if hardware_compatibility_dict:
        if vehicle_model not in spreadsheet.recorded_vehicle_model:
            # 保存 header
            ecu_names = list(hardware_compatibility_dict.keys())
            ecu_names.sort()
            spreadsheet.add_header(vehicle_model, ecu_names)
    else:
        logger.error(f"没有解析到 {vehicle_model} 车型对应的软硬件兼容表")
        spreadsheet.save_row(vehicle_model, vin, stage, "", [f"没有解析到 {vehicle_model} 车型对应的软硬件兼容表"])
        return

    sample_time = ecu_version_and_sample_time.get("sample_time")  # s
    sample_time_str = get_date_from_timestamp(sample_time)
    ecu_version_dict = ecu_version_and_sample_time.get("ecu_version_map", {})

    replaced_hardware_list = []
    for key, value in ecu_version_dict.items():
        ecu_name = value.get("ecu")
        current_hardware_pn = value.get("hardware_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())

        # 空
        if len(current_hardware_pn) == 0:
            logger.warning(
                f"{vin} {ecu_name} hardware_pn: {current_hardware_pn}, is empty.")
            replaced_hardware_list.append((ecu_name, 2))
            continue
        # 乱码
        elif not current_hardware_pn.split()[0].isalnum():
            logger.warning(
                f"{vin} {ecu_name} hardware_pn: {current_hardware_pn}, is messy code.")
            replaced_hardware_list.append((ecu_name, 3))
            continue

        # 不兼容
        if ecu_name in hardware_compatibility_dict.keys():
            software_pn_and_hardware_pn_list = hardware_compatibility_dict[ecu_name]

            hardware_matched = False
            hardware_pn_set = set()
            for software_pn, hardware_pn_list in software_pn_and_hardware_pn_list:
                hardware_pn_set.update(set(hardware_pn_list))
                if current_hardware_pn in hardware_pn_list or len(hardware_pn_list) == 0:
                    hardware_matched = True
                    break
            if not hardware_matched:
                logger.warning(
                    f"{vin} {ecu_name} hardware_pn: {current_hardware_pn}, not in {hardware_pn_set}")
                replaced_hardware_list.append((ecu_name, 1))

    spreadsheet.save_row(vehicle_model, vin, stage, sample_time_str, replaced_hardware_list)


def is_opened(file_path: str):
    try:
        sheet = parse_spreadsheet.Spreadsheet(file_path)
        sheet.close_file()
        return False
    except:
        logger.error(f"{file_path} is opened.")
        return True


def check_hardware_compatibility():
    vehicle_spreadsheet = None
    args_vin = args.vin
    args_path = args.path

    if args_vin and args_path:
        logger.info(f"Ignore VINs in {args_path}, get VINs from --vin {args_vin}")
        vins = args_vin
    elif args_vin:
        logger.info(f"Get VINs from --vin {args_vin}")
        vins = args_vin
    elif args_path:
        args_path = os.path.realpath(args_path)
        logger.info(f"Get VINs in {args_path}")
        if is_opened(args_path):
            logger.error(f"请先关闭 {args_path} 文件")
            return
        vehicle_spreadsheet = parse_spreadsheet.VehicleSpreadsheet(args_path)
        vins = vehicle_spreadsheet.get_vins()
        if not args.ecu:
            vehicle_spreadsheet.remove_other_sheets()
    else:
        logger.error(f"Can't get VINs in XXX.xlsx by add --path XXX option, or from --vin XXX option")
        return

    if vehicle_spreadsheet is None:
        now_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y%m%d_%H%M%S")
        if len(vins) == 1:
            path = os.path.join(dir_path, f"{vins[0]}_软件刷写硬件兼容情况_{now_str}.xlsx")
        else:
            path = os.path.join(dir_path, f"车辆软件刷写硬件兼容情况_{now_str}.xlsx")
        vehicle_spreadsheet = parse_spreadsheet.VehicleSpreadsheet(path)

    num = 1
    vins.sort()
    for vin in vins:
        logger.info(f"{vin}")
        check_hardware_compatibility_for_one_vehicle(vin, vehicle_spreadsheet)
        logger.info(f"Save {num}/{len(vins)}")
        num += 1
        print()


def get_need_to_replaced_hardware():
    args_path = args.path
    if args_path:
        args_path = os.path.realpath(args_path)
        logger.info(f"Get VINs in {args_path}")
        if is_opened(args_path):
            logger.error(f"请先关闭 {args_path} 文件")
            return
        vehicle_spreadsheet = parse_spreadsheet.VehicleSpreadsheet(args_path)
        vin_stage_list = vehicle_spreadsheet.get_vin_stage()
    else:
        logger.error(f"Can't get VINs in XXX.xlsx by add --path XXX option")
        return

    replace_path = os.path.realpath(args.replace)
    logger.info(f"Result will save to {replace_path}")
    if is_opened(replace_path):
        logger.error(f"请先关闭 {replace_path} 文件")
        return
    result_spreadsheet = parse_spreadsheet.ReplaceHardware(replace_path)

    num = 1
    vin_stage_list.sort(key=lambda a_list: a_list[-1])
    for vin, stage in vin_stage_list:
        logger.info(f"{vin}")
        get_replaced_hardware_for_one_vehicle(vin, stage, result_spreadsheet)
        logger.info(f"Save {num}/{len(vin_stage_list)}")
        num += 1
        print()


if __name__ == "__main__":
    # 创建解析器对象
    parser = argparse.ArgumentParser()

    # 添加选项
    parser.add_argument("-v", "--vin", nargs="+", help="车辆 VIN", type=str)
    parser.add_argument("-p", "--path", help="XXX.xlsx 文件路径，存储需要查询车辆 VIN", type=str)
    parser.add_argument("-r", "--replace", help="XXX 车辆需更换硬件.xlsx 文件路径", type=str)
    parser.add_argument("-e", "--ecu", help="只保存车辆 ECU 软硬件零件号", action="store_true")
    parser.add_argument("-d", "--did", help="使用标识文件，若不指定则从 TVAS 上读取", action="store_true")

    # 解析命令行参数
    args = parser.parse_args()

    url = "https://tvas.nioint.com/tvas/vehicleResource"
    element_id = "container"
    login = login_nio.LoginNio(url, element_id)
    cookie_dict = login.get_cookie()
    get_ecu_version_request = get_ecu_version.GetEcuVersion(cookie_dict)

    dir_path = os.path.dirname(__file__)

    try:
        # 执行软件更换分析
        if args.replace:
            get_need_to_replaced_hardware()
        # 执行软件硬件兼容分析
        else:
            check_hardware_compatibility()
    # Ctrl-C 结束程序
    except KeyboardInterrupt:
        logger.info("Close script")
