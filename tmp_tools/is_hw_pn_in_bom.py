#!/usr/bin/python3

import os
import argparse
import datetime
import openpyxl

import login_nio
import get_ecu_version
import parse_spreadsheet
from log_config import logger


def is_opened(file_path: str):
    try:
        # 尝试打开文件
        wb = openpyxl.load_workbook(file_path)
        # 关闭文件
        wb.save(file_path)
        wb.close()
        return False
    except:
        logger.error(f"{file_path} is opened.")
        return True


def save_excel(ecu_sw_pn_list, vehicle_excel, sheet_name):
    sheet = vehicle_excel.wb.create_sheet(sheet_name)
    for i in ecu_sw_pn_list:
        sheet.append(i)
    vehicle_excel.close_file()


# 10 或 13位 时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


def get_vehicle_sw_pn():
    global get_ecu_version_by_fota

    args_path = args.path
    args_path = os.path.realpath(args_path)
    logger.info(f"Get VINs in {args_path}")
    if is_opened(args_path):
        logger.error(f"请先关闭 {args_path} 文件")
        return

    vehicle_excel = parse_spreadsheet.VehicleSpreadsheet(args_path)
    vehicle_excel.remove_other_sheets()
    vins = vehicle_excel.get_vins()

    ebom = parse_spreadsheet.Bom(args.bom)
    hw_pn_set = ebom.get_hw_pns()

    num = 1
    ecu_sw_pn_list = []

    excel_header = ['ACM', 'ACU', 'ADF', 'AMP', 'ASB_D', 'BCU', 'BMS', 'BTA1', 'BTA2', 'BTA3', 'BTA4', 'BTA5', 'BTA6',
                    'CDF', 'CNSL_I', 'DCM_FL', 'DCM_FR', 'DCM_RL', 'DCM_RR', 'DHS_FL', 'DHS_FR', 'DHS_RL', 'DHS_RR',
                    'EPS1', 'ETC', 'FAS_FL', 'FAS_FR', 'FAS_RL', 'FAS_RR', 'FI_R', 'FLM_L', 'FLM_R', 'FTU', 'HUD',
                    'HVC', 'ICS', 'IC_M', 'ILM_DYN_DFL', 'ILM_DYN_DFR', 'ILM_DYN_DRL', 'ILM_DYN_DRR', 'ILM_DYN_IPL',
                    'ILM_DYN_IPR', 'IPU_F', 'IPU_R', 'ISRVM_S', 'LBM_AR', 'LBM_BF', 'LID_MAIN', 'LID_SL', 'LID_SR',
                    'MCM_D', 'MCM_P', 'MCM_RL', 'MCM_RR', 'MICRO_LED_L', 'MICRO_LED_R', 'NFC', 'NOMI', 'PDS_FL',
                    'PDS_FR', 'PDS_RL', 'PDS_RR', 'PEU_F', 'PEU_R', 'PTC_F', 'RAD_FC', 'RBU', 'REFRIGERATOR', 'RFR',
                    'RLM', 'RLS', 'RWS', 'SAF', 'SCM', 'SCU_D', 'SCU_P', 'SCU_RL', 'SCU_RR', 'SRM', 'STF1', 'SWC',
                    'VDF', 'WLC_D', 'WLC_P', 'WLC_RL', 'ZONE_FTE', 'ZONE_FTM', 'ZONE_REE', 'ZONE_REM']

    for vin in vins:
        logger.info(f"{vin}")

        vid = None
        vehicle_info_dict = get_ecu_version_by_fota.query_vehicle_info(vin)
        if vehicle_info_dict:
            vid = vehicle_info_dict.get("vid")
            platform = vehicle_info_dict.get("platform", "")
            npt = vehicle_info_dict.get("npt", "")
            ga = vehicle_info_dict.get("ga", "")
        else:
            vid = None
            platform = npt = ga = "无法获取"

        hw_pns = []
        sample_time_str = ""
        ecu_version = get_ecu_version_by_fota.query_ecu_version_by_fota(vid)
        if ecu_version:
            sample_time = ecu_version.get("sample_time", 0)  # s
            sample_time_str = get_date_from_timestamp(sample_time)

            for ecu_name in excel_header:
                hw_pn = ecu_version.get("ecu_version_map").get(ecu_name,{}).get("hardware_pn", "")
                if hw_pn and hw_pn not in hw_pn_set:
                    hw_pns.append(hw_pn)

        ecu_sw_pn_list.append([vin, vid, platform, npt, ga, sample_time_str] + hw_pns)
        logger.info(f"Get {num}/{len(vins)}")
        num += 1
        print()

    ecu_sw_pn_list.sort(key=lambda seq: (seq[2], seq[3], seq[4], seq[0]))
    ecu_sw_pn_list.insert(0, ["VIN", "VID", "平台", "NPT", "GA", "采样时间"] + excel_header)
    save_excel(ecu_sw_pn_list, vehicle_excel, "车辆信息")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--path", required=True, help="XXX.xlsx 文件路径，存储需要查询车辆 VIN", type=str)
    parser.add_argument("-b", "--bom", required=True, help="XXX.xlsx 文件路径，存储车型车型的 EBOM", type=str)
    args = parser.parse_args()

    # url = "https://fota-web-stg.nioint.com/v2/#/fota/vehicles"
    # element_id = "app"
    # login = login_nio.LoginNio(url, element_id)
    # cookie_dict = login.get_cookie()
    # cookie = "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()])
    cookie = "FOTA-LGUSER=falcon.fan; lang=zh_CN; tenant=nio_ota; page-gateway-secure-sid-cn-prod=s%3AhPm_-cqugxefEQb0Vqn79uH4YOfO3N9m.C6ZHCArvBz9sxYehVckuWjpzAJQ4NUGBLDK%2BhsXGANY; page-gateway-secure-sid-plm=s_hPm_-cqugxefEQb0Vqn79uH4YOfO3N9m.0ba647080aef073f6cc587a155c92e5a3a730094383541812c32be86c5c600d6; proxyserver_prod=s%3AuaFcGPpcc3jgc1NzAxfJigKbi8YCe7jP.twuzuxV%2BEh9nCidY1xCLqus7LZnnJz28y2g5qX59Tzc; page-gateway-sid-cn-prod=s%3AGPK3KpjX2tCeVjR1MFMasWUEE7wXOUhM.G8xaJfK6SNmyYJyabLgoKRlIvY1BPBfXwqXzIDbmyz4; page-gateway-sid-plm=s_GPK3KpjX2tCeVjR1MFMasWUEE7wXOUhM.1bcc5a25f2ba48d9b2609c9a6cb828291948bd8d413c17d7c2a5f32036e6cb3e; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22falcon.fan%22%2C%22first_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThhY2YyYzA2MTdlYWEtMDRkOWVmMDBlOWJjNmY4LTI2MDMxZTUxLTE0NzQ1NjAtMThhY2YyYzA2MTgyOWViIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiZmFsY29uLmZhbiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22falcon.fan%22%7D%2C%22%24device_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%7D; NIO-FOTA=MTcyNTI1NjQxNXxEdi1CQkFFQ180SUFBUkFCRUFBQV9nRmtfNElBQndaemRISnBibWNNQkFBQ2RHc0djM1J5YVc1bkREMEFPekl1TURSWFNFNVpNelZTV0RKUVRUSkxOVTR6VjA1Tk5FRlJRVTlMUTBaVFVrOUdTbGxYUlZkTlJsWlBOMHRMVmtoU1JVWkpOMEV0TFMwdEJuTjBjbWx1Wnd3RUFBSmpiQVp6ZEhKcGJtY01RZ0JBTkRFMFptTmxNMlUzWWpjM01HUTVPRFZrTWpOa05EVXhZbVF3WkRobFlqYzNZV1E0WW1Vd1lqZ3pOR0k0WldZeVl6WXpOR1JrWWpnelpXWXpaVE01TndaemRISnBibWNNQkFBQ2FXUUdjM1J5YVc1bkRDWUFKR00zWXpabE16RTRMVGczTmpBdE9EbGpaUzFoTXpBeExXSTFOV0ZtTWpRNE1tVTNZd1p6ZEhKcGJtY01CQUFDYVhBR2MzUnlhVzVuREE0QURERXdMakV4TVM0MExqRXlNd1p6ZEhKcGJtY01CQUFDYzNRRmFXNTBOalFFQmdEOHphcWx2Z1p6ZEhKcGJtY01CQUFDZEdVR2MzUnlhVzVuREFrQUIyNXBiMTl2ZEdFR2MzUnlhVzVuREFRQUFteDBCbk4wY21sdVp3d0ZBQU5UVTA4PXyVAmcJ4vU9iFTP7DfCwfn8Wh0kJzu_jdYTmdkoFdCYCA==; fota-clientid=483c57b8914a0cb0ec50affb60aadb25"
    get_ecu_version_by_fota = get_ecu_version.GetEcuVersionByFota(cookie, "https://fota-web-stg.nioint.com/")

    try:
        get_vehicle_sw_pn()
        # Ctrl-C 结束程序
    except KeyboardInterrupt:
        logger.debug("Close script")
