#!/usr/bin/python3

import os
import argparse
import datetime
import openpyxl

import login_nio
import get_ecu_version
import parse_spreadsheet
from log_config import logger


def is_opened(file_path: str):
    try:
        # 尝试打开文件
        wb = openpyxl.load_workbook(file_path)
        # 关闭文件
        wb.save(file_path)
        wb.close()
        return False
    except:
        logger.error(f"{file_path} is opened.")
        return True


def save_excel(ecu_sw_pn_list, vehicle_excel, sheet_name):
    sheet = vehicle_excel.wb.create_sheet(sheet_name)
    for i in ecu_sw_pn_list:
        sheet.append(i)
    vehicle_excel.close_file()


# 10 或 13位 时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


def get_vehicle_sw_pn():
    global get_ecu_version_by_fota

    args_path = args.path
    args_path = os.path.realpath(args_path)
    logger.info(f"Get VINs in {args_path}")
    if is_opened(args_path):
        logger.error(f"请先关闭 {args_path} 文件")
        return

    vehicle_excel = parse_spreadsheet.VehicleSpreadsheet(args_path)
    vehicle_excel.remove_other_sheets()
    vins = vehicle_excel.get_vins()

    num = 1
    ecu_sw_pn_list = []

    for vin in vins:
        logger.info(f"{vin}")

        vid = None
        vehicle_info_dict = get_ecu_version_by_fota.query_vehicle_info(vin)
        if vehicle_info_dict:
            vid = vehicle_info_dict.get("vid")
            platform = vehicle_info_dict.get("platform", "")
            npt = vehicle_info_dict.get("npt", "")
            ga = vehicle_info_dict.get("ga", "")
        else:
            vid = None
            platform = npt = ga = "无法获取"

        ecu_version = get_ecu_version_by_fota.query_ecu_version_by_fota(vid)
        if ecu_version:
            adc_sw_pn = ecu_version.get("ecu_version_map").get("ADC").get("software_pn", "")
            cdc_sw_pn = ecu_version.get("ecu_version_map").get("CDC").get("software_pn", "")
            sample_time = ecu_version.get("sample_time", 0)  # s
            sample_time_str = get_date_from_timestamp(sample_time)
        else:
            adc_sw_pn = "无法获取"
            cdc_sw_pn = "无法获取"
            sample_time_str = "无法获取"

        ecu_sw_pn_list.append([vin, vid, platform, npt, ga, adc_sw_pn, cdc_sw_pn, sample_time_str])
        logger.info(f"Get {num}/{len(vins)}")
        num += 1
        print()

    ecu_sw_pn_list.sort(key=lambda seq: (seq[2], seq[3], seq[4], seq[0], seq[5], seq[6]))
    save_excel(ecu_sw_pn_list, vehicle_excel, "车辆信息")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--path", required=True, help="XXX.xlsx 文件路径，存储需要查询车辆 VIN", type=str)
    args = parser.parse_args()

    # url = "https://fota-web-stg.nioint.com/v2/#/fota/vehicles"
    # element_id = "app"
    # login = login_nio.LoginNio(url, element_id)
    # cookie_dict = login.get_cookie()
    # cookie = "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()])
    cookie = "FOTA-LGUSER=falcon.fan; lang=zh_CN; tenant=nio_ota; page-gateway-secure-sid-cn-prod=s%3AJndJiyRsJvJoESaBjJ3RWjPYj8T-SIyT.fTEF210w9g%2By4TvUjffXJEyqpv3QYWjJZVA%2BS5hWC%2Bo; page-gateway-secure-sid-plm=s_JndJiyRsJvJoESaBjJ3RWjPYj8T-SIyT.7d3105db5d30f60fb2e13bd48df7d7244caaa6fdd06168c965503e4b98560bea; proxyserver_prod=s%3ALgUfYbE0TIJVGoOMl9SG4vyA-Ah4KgLb.z21Tm%2FXSqdgx3DOvOatBqzFvwFCWtPoZCkv8wV0Bij8; vms_timezone=Asia/Shanghai; proxyserver_stg=s%3AvqhiGqI9_O_wgESlGQb5iriJFlGSvohM.oajA3Jv4eN2FD5dL5hEmw5eKxQ224psshnSZcpEAskk; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22falcon.fan%22%2C%22first_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJmYWxjb24uZmFuIiwiJGlkZW50aXR5X2Nvb2tpZV9pZCI6IjE4YWNmMmMwNjE3ZWFhLTA0ZDllZjAwZTliYzZmOC0yNjAzMWU1MS0xNDc0NTYwLTE4YWNmMmMwNjE4MjllYiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22falcon.fan%22%7D%2C%22%24device_id%22%3A%2218acf2c0617eaa-04d9ef00e9bc6f8-26031e51-1474560-18acf2c061829eb%22%7D; page-gateway-sid-cn-prod=s%3Az11nlNOvuiFrRM4KdtgdOmEu0doFI4W4.vpwBbL2vqn6cf%2Bh4s3ZQk%2FxLYE3ZO6SFTeiOXbkt9O4; page-gateway-sid-plm=s_z11nlNOvuiFrRM4KdtgdOmEu0doFI4W4.be9c016cbdafaa7e9c7fe878b3765093fc4b604dd93ba4854de88e5db92df4ee; NIO-FOTA=MTcyMzcxNTE1N3xEdi1CQkFFQ180SUFBUkFCRUFBQV9nRmtfNElBQndaemRISnBibWNNQkFBQ2JIUUdjM1J5YVc1bkRBVUFBMU5UVHdaemRISnBibWNNQkFBQ2RHc0djM1J5YVc1bkREMEFPekl1TURSSFNVNVVSVFJVVWxkQ1VqTmFTMEpUU1VKSU1rbFBUa016VTA5RlVGWldWbFZSVGtoWVMxRTFORVkzUnpaTk1raFFOMUV0TFMwdEJuTjBjbWx1Wnd3RUFBSmpiQVp6ZEhKcGJtY01RZ0JBTkRFMFptTmxNMlUzWWpjM01HUTVPRFZrTWpOa05EVXhZbVF3WkRobFlqYzNZV1E0WW1Vd1lqZ3pOR0k0WldZeVl6WXpOR1JrWWpnelpXWXpaVE01TndaemRISnBibWNNQkFBQ2FXUUdjM1J5YVc1bkRDWUFKR00zWXpabE16RTRMVGczTmpBdE9EbGpaUzFoTXpBeExXSTFOV0ZtTWpRNE1tVTNZd1p6ZEhKcGJtY01CQUFDYVhBR2MzUnlhVzVuREE0QURERXdMakV4TVM0MExqRXlNd1p6ZEhKcGJtY01CQUFDYzNRRmFXNTBOalFFQmdEOHpYdWNxZ1p6ZEhKcGJtY01CQUFDZEdVR2MzUnlhVzVuREFrQUIyNXBiMTl2ZEdFPXxATXxgVg2CfAj4kafZCWJ5d23W-5VislFJPQlcpASbCQ==; fota-clientid=1fc67de2178dbb64df3e2af82910e079"
    get_ecu_version_by_fota = get_ecu_version.GetEcuVersionByFota(cookie, "https://fota-web-stg.nioint.com/")

    try:
        get_vehicle_sw_pn()
        # Ctrl-C 结束程序
    except KeyboardInterrupt:
        logger.debug("Close script")
