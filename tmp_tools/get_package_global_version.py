#!/usr/bin/python3

import os
import argparse
import datetime
import openpyxl

import login_nio
import get_ecu_version
import parse_spreadsheet
from log_config import logger


def is_opened(file_path: str):
    try:
        # 尝试打开文件
        wb = openpyxl.load_workbook(file_path)
        # 关闭文件
        wb.save(file_path)
        wb.close()
        return False
    except:
        logger.error(f"{file_path} is opened.")
        return True


def save_excel(global_version_list, vehicle_excel, sheet_name):
    sheet = vehicle_excel.wb.create_sheet(sheet_name)
    for i in global_version_list:
        vin = i[0]
        global_version = i[1]
        sample_time_str = i[2]
        sheet.append([vin, global_version, sample_time_str])
    vehicle_excel.close_file()


def get_current_ecu_version(vin: str):
    res = get_ecu_version_request.query_by_vin(vin)
    if res is None:
        return None

    return get_ecu_version_request.query_ecu_version(vin)


# 10 或 13位 时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


def get_package_global_version():
    args_path = args.path
    args_path = os.path.realpath(args_path)
    logger.info(f"Get VINs in {args_path}")
    if is_opened(args_path):
        logger.error(f"请先关闭 {args_path} 文件")
        return

    vehicle_excel = parse_spreadsheet.VehicleSpreadsheet(args_path)
    vehicle_excel.remove_other_sheets()
    vins = vehicle_excel.get_vins()

    num = 1
    global_version_list = []
    for vin in vins:
        logger.info(f"{vin}")
        ecu_version = get_current_ecu_version(vin)
        if ecu_version:
            package_global_version = ecu_version.get("package_global_version", "")
            sample_time = ecu_version.get("sample_time", 0)  # s
            sample_time_str = get_date_from_timestamp(sample_time)
        else:
            package_global_version = "无法获取"
            sample_time_str = "无法获取"

        global_version_list.append((vin, package_global_version, sample_time_str))
        logger.info(f"Get {num}/{len(vins)}")
        num += 1
        print()

    global_version_list.sort(key=lambda x: x[0])
    save_excel(global_version_list, vehicle_excel, "VIN 排序")
    global_version_list.sort(key=lambda x: x[1])
    save_excel(global_version_list, vehicle_excel, "整车版本排序")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--path", required=True, help="XXX.xlsx 文件路径，存储需要查询车辆 VIN", type=str)
    args = parser.parse_args()

    url = "https://tvas.nioint.com/tvas/vehicleResource"
    element_id = "container"
    login = login_nio.LoginNio(url, element_id)
    cookie_dict = login.get_cookie()
    get_ecu_version_request = get_ecu_version.GetEcuVersion(cookie_dict)

    try:
        get_package_global_version()
        # Ctrl-C 结束程序
    except KeyboardInterrupt:
        logger.debug("Close script")
