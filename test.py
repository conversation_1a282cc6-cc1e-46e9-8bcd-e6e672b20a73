# import os
# # __file__ 是当前模块的名称
# print(__file__)
# abs_path = os.path.abspath(__file__)
# # abs_path 是当前模块的绝对路径
# print(abs_path)
# '''
# test.py - __file__
# /home/<USER>/Downloads/hardware_compatibility/test.py - abs_path
# '''
# # os.path.dirname(__file__) - 获取目录部分
# # /home/<USER>/Downloads/hardware_compatibility
# project_dir = os.path.dirname(abs_path)
# print(project_dir)

# # hardware_compatibility
# project_name = os.path.basename(project_dir)
# print(project_name)

# # hardware_compatibility
# project_name = project_dir.split('/')[-1].split('\\')[-1]
# print(project_name)

# '''
# 第一次split("/") - 按Unix/Linux路径分隔符分割
# # Linux/macOS 路径：/home/<USER>/Downloads/hardware_compatibility
# parts = project_dir.split("/")
# # 结果：['', 'home', 'nio', 'Downloads', 'hardware_compatibility']

# # Windows 路径：C:\Users\<USER>\Downloads\hardware_compatibility  
# parts = project_dir.split("/")
# # 结果：['C:\\Users\\<USER>\\Downloads\\hardware_compatibility']  # 没有分割

# 取最后一个元素 [-1]
# # Linux/macOS 情况：
# last_part = parts[-1]  # 'hardware_compatibility'

# # Windows 情况：
# last_part = parts[-1]  # 'C:\\Users\\<USER>\\Downloads\\hardware_compatibility'

# 第二次split("\\") - 按Windows路径分隔符分割
# # Linux/macOS 情况（已经是项目名）：
# final_parts = 'hardware_compatibility'.split("\\")
# # 结果：['hardware_compatibility']  # 没有反斜杠，不会分割

# # Windows 情况：
# final_parts = 'C:\\Users\\<USER>\\Downloads\\hardware_compatibility'.split("\\")
# # 结果：['C:', 'Users', 'nio', 'Downloads', 'hardware_compatibility']

# 再次取最后一个元素 [-1]
# # 无论哪种情况，最终都得到：
# project_name = final_parts[-1]  # 'hardware_compatibility'
# '''

# '''
# 虽然在大多数情况下两种方式结果相同，但 os.path.basename() 是更好的选择，因为它：

# 更可读：语义明确
# 更健壮：处理边界情况更好
# 更标准：符合Python最佳实践
# 更安全：内置错误处理
# 原项目中使用字符串分割可能是为了确保在某些特殊环境下的兼容性，但在现代Python开发中，推荐使用 os.path.basename()。
# '''

# data_dict = {
#     "sample_time": "2025-07-02 14:30:00",
#     "vehicle_id": "V00123",
#     "ecu_version_map": {
#         "engine": "V2.1.5",
#         "transmission": "V1.8.0"
#     },
#     "other_field": "无关数据"  # 该字段不会被提取
# }

# result_dict = {
#     "sample_time": data_dict.get("sample_time"),
#     "vehicle_id": data_dict.get("vehicle_id"),
#     "ecu_version_map": data_dict.get("ecu_version_map", {}),
# }

# print(result_dict)